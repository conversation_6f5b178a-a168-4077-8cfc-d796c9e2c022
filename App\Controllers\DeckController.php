<?php

namespace App\Controllers;

use App\Models\{User, Deck};
use App\Controllers\{ProductController};
use tools\deckanalyzer\DeckAnalyzer;
use tools\deckbuilder\DeckBuilder;

class DeckController
{
    private $connectionDB;
    private $ProductController;

    public function __construct(\PDO $connectionDB)
    {
        $this->connectionDB = $connectionDB;
        $this->ProductController = new ProductController($connectionDB);
    }

    public function analyze(array $namesCards, int $anaEvo, string $level, float $version = 1.0): mixed
    {
        $resPurchase = $this->ProductController->purchaseDeckAnalyzer($level, $version);

        if (!$resPurchase['state'] == 'success')
            throw new \Exception("Error al Hacer el Pago");

        $Deck = new Deck($namesCards, 11, $anaEvo);
        $analysisData = [
            'averages' => [],
            'data' => [],
            'level' => $level,
            'defenseTypeNames' => null,
            'defenseTypeDescriptions' => null
        ];
        $message = '';

        $defenseJsonPath = PATH_ROOT . "App/Data/tools/DeckAnalyzer/defenseCoverage.json";
        $defenseJson = file_exists($defenseJsonPath) ? json_decode(file_get_contents($defenseJsonPath)) : (object) ['names' => new \stdClass(), 'descriptions' => new \stdClass()];

        $statCards = json_decode(file_get_contents(PATH_ROOT . 'App/Data/cards/statCards.json'));
        $constants = json_decode(file_get_contents(PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Constants.json'));
        $pathsStrategies = [
            "General" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/General.json',
            "Defensa" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/Defense.json',
            "Adaptavilidad" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/Adaptability.json',
            "Agresivo" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/Aggressive.json',
            "Bait" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/Bait.json',
            "Ciclo" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/Cycle.json',
            "Precion" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/Pressure.json',
            "Push" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/Push.json',
            "SplitPush" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/SplitPush.json'
        ];

        $DeckAnalyzer = new DeckAnalyzer($statCards, $constants, $pathsStrategies, $Deck, $level);

        // Common analysis for all levels
        $analysisData['data'] = [
            'shortCycle' => $DeckAnalyzer->getShortCycle(),
            'averageElixirCost' => $DeckAnalyzer->getAverageElixirCost(),
            'archetype' => $DeckAnalyzer->identifyArchetype()
        ];
        $analysisData['averages']['Ataque'] = $DeckAnalyzer->getBattleRolesScores('attack');
        $analysisData['averages']['Defensa'] = $DeckAnalyzer->getBattleRolesScores('defense');

        // Analysis specific to Intermediate and Advanced levels
        if ($level === 'intermediate' || $level === 'advanced') {
            $analysisData['weaknesses'] = $DeckAnalyzer->getDeckWeaknesses();
            $analysisData['defenseCoverage'] = $DeckAnalyzer->getDefenseTypesCoverage();
            $message = '<span class="cs-color-VibrantTurquoise text-center">Mazo Analizado</span>';
        }

        // Analysis specific to Advanced level
        if ($level === 'advanced') {
            $analysisData['averages']['Versatilidad'] = $DeckAnalyzer->getScoreVersatility();
            $analysisData['averages']['Sinergia'] = $DeckAnalyzer->getScoreSynergy();
            $analysisData['averages']['Versatilidad']['averagesPerGroup'] = $DeckAnalyzer->getBattleRolesScoresByGroup($analysisData['averages']['Versatilidad']);
        }

        $analysisData['defenseTypeNames'] = $analysisData['defenseTypeNames'] ?? $defenseJson->names;
        $analysisData['defenseTypeDescriptions'] = $analysisData['defenseTypeDescriptions'] ?? $defenseJson->descriptions;

        return [
            "message" => $message,
            "result" => $analysisData,
            "balance" => [
                "gems"=> $_SESSION['Gems'],
                "coins" => $_SESSION['Coins']
            ]
        ];
    }

    public function create(array $params): array
    {
        $res = ["message" => '', 'data' => '', 'balance' => ''];

        if ($params['level'] == "advanced" && !is_float($params['MazOpt']) && $params['MazOpt'] > 3) {
            $res['res'] = '<span class="cs-color-IntenseOrange text-center">Las opciones de mazos no pueden ser mayor a 3</span>';
            return $res;
        }

        $payDeckBuilder = $this->ProductController->purchaseDeckBuilder($params['level'], $params['version']);

        if ($payDeckBuilder['state'] == 'success') {
            $statCards = json_decode(file_get_contents(PATH_ROOT . 'App/Data/cards/statCards.json'));
            $constants = json_decode(file_get_contents(PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Constants.json'));
            $DeckBuilder = new DeckBuilder($statCards, $constants, $params['level']);
            $DeckCreated = $DeckBuilder->create(
                $params['winConditionName'],
                $params['attackLevel'],
                // Options Intermediate
                $params['ChampeonName'] ?? 'Libre',
                $params['Estrategias'] ?? 'Control',
                $params['PrElixir'] ?? '3.0',
                // Options Advanced
                $params['iteration'] ?? 1,
                $params['MazOpt'] ?? 1,
                $params['evo'] ?? 0
            );
            $res['result'] = $DeckCreated;
            $res['change'] =[
                "gems"=> $_SESSION['Gems'],
                "coins" => $_SESSION['Coins']
            ];
        } else {
            throw new \Exception("Error al Hacer el Pago");
        }
        return $res;
    }
}
