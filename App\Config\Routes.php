<?php
session_start();
define('PATH_ROOT', realpath(__DIR__ . '/../../') . '/');
require_once __DIR__ . '/Autoloader.php';

use App\Controllers\{UserController, DeckController, CardController, ProductController};
use App\Config\{Router, Config};
use App\Services\{ConnectionDBPool};

function setSessionInvitado(&$TypeAcount)
{
    $usarr = [
        'id_Usu' => null,
        'Tag' => null,
        'Correo' => null,
        'FechaNac' => null,
        'Verificado' => null,
        'Usuario' => null,
        'Estado' => null,
        'created_at' => null,
        'TypeAcount' => null,
        'Avatar' => 'defect.webp',
        'Banner' => 'defect.webp',
        'Nombre' => 'invitado',
        'Coins' => 0,
        'Gems' => 0,
        'Mazos' => '["", "", "", "", "", "", "", "", "", ""]'
    ];

    $_SESSION['id'] = $usarr['id_Usu'];
    $_SESSION['created_at'] = $usarr['created_at'];
    $_SESSION['Tag'] = $usarr['Tag'];
    $_SESSION['Usuario'] = $usarr['Usuario'];
    $_SESSION['Avatar'] = $usarr['Avatar'];
    $_SESSION['Banner'] = $usarr['Banner'];
    $_SESSION['Nombre'] = $usarr['Nombre'];
    $_SESSION['Correo'] = $usarr['Correo'];
    $_SESSION['FechaNac'] = $usarr['FechaNac'];
    $_SESSION['Verificado'] = $usarr['Verificado'];
    $_SESSION['Coins'] = $usarr['Coins'];
    $_SESSION['Gems'] = $usarr['Gems'];
    $_SESSION['Mazos'] = $usarr['Mazos'];
    $_SESSION['State'] = $usarr['Estado'];
    $_SESSION['TypeAcount'] = 'invitado';

    $_SESSION['id_Ses'] = null;
    $_SESSION['SessionIdUser'] = null;
    $_SESSION['SessionState'] = null;
    $_SESSION['SessionToken'] = null;
    $_SESSION['SessionIP'] = null;
    $_SESSION['SessionUserAgent'] = null;
    $_SESSION['SessionCreatedAt'] = null;

    $TypeAcount = $_SESSION['TypeAcount'];

    setcookie('Mazos', $_SESSION['Mazos'], 0, '/');
    setcookie('TypeAcount', $_SESSION['TypeAcount'], 0, '/');
    //cookies de configuracion
    setcookie('sound_effects', 'true', 0, '/');
}

try {
    $TypeAcount = isset($_SESSION['TypeAcount']) ? $_SESSION['TypeAcount'] : null;

    // Si es null, intentar obtener una conexión para insertar sesión de invitado
    if ($TypeAcount == null) {
        setSessionInvitado($TypeAcount);
    } else {
        $requiredCookies = ['Mazos', 'TypeAcount', 'Timezone', 'byOrdenCards', 'nmazo', 'sound_effects'];
        $allCookiesRequired = true;

        foreach ($requiredCookies as $cookie) {
            if (!isset($_COOKIE[$cookie])) {
                $allCookiesRequired = false;
                break;
            }
        }

        if (!$allCookiesRequired) { // Si falta alguna cookie, iniciar una nueva sesión
            if (empty($_COOKIE)) {
                $router->respond(400, "error", "Las cookies están deshabilitadas. Por favor, habilita las cookies para continuar.");
            }

            if ($TypeAcount == 'invitado') {
                setSessionInvitado($TypeAcount);
            } else { //de momento solo se puede iniciar sesión como invitado
                setSessionInvitado($TypeAcount);
            }
        }
    }

    Config::initialSetup();
    $router = new Router();

    $uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    $method = $_SERVER['REQUEST_METHOD'];
    $_INPUT = json_decode(file_get_contents('php://input'), true);

    // Delete /api of $uri if it exists, only for authorized domain
    if (
        ($_SERVER['SERVER_NAME'] == 'clashstrategic.great-site.net' || $_SERVER['SERVER_NAME'] == 'localhost') &&
        strpos($uri, '/api') === 0
    )
        $uri = substr($uri, 4);

    if (!in_array($uri, array_keys(Config::$endpoints)))
        $router->respond(400, "error", "Bad request. Invalid endpoint.");

    if (!in_array($method, Config::$endpoints[$uri]['validMethods']))
        $router->respond(400, "error", "Bad request. Invalid method.");

    if ($method == 'POST' && !Config::validateIncomingData($_INPUT, $uri))
        $router->respond(400, "error", "Bad request. Invalid data.");

    $connectionDB = null;
    $needsDBConnection = Config::$endpoints[$uri]['requireDBConnection'];

    if ($needsDBConnection) {
        $DatabasePool = new ConnectionDBPool(Config::getParamDB($_SERVER['SERVER_NAME']));
        $connectionDB = $DatabasePool->getConnection();
        if ($connectionDB === null) {
            $router->respond(500, "error", "the server is busy, please try again later.");
        }
    }

    // Verificar la sesión solo si es necesario
    if ($TypeAcount == 'google' && $needsDBConnection) {
        $userSession = new UserController($connectionDB, $_SESSION['id']);
        if (!$userSession->isSessionActive()) {
            $userSession->logout();
            $_SERVER['SERVER_NAME'] == 'localhost' ? header('Location: /clash-strategic-api/home') : header('Location: /home');
            return; // Importante: Agrega un return para evitar ejecutar el resto del código
        }
    }

    if ($TypeAcount == 'google' || $TypeAcount == 'sistem' || $TypeAcount == 'invitado') {
        if ($needsDBConnection) {
            $router->addRoute('GET', '/v1/session', [UserController::class, 'getSession'], ["class" => [], "method" => []]);
            $router->addRoute('POST', '/v1/session', [UserController::class, 'insertSession'], ["class" => [], "method" => [$connectionDB, $_INPUT['data'] ?? []]]);
            $router->addRoute('DELETE', '/v1/session', [UserController::class, 'logout'], ["class" => [$connectionDB, $_SESSION['id']], "method" => []]);

            if ($TypeAcount == 'google' || $TypeAcount == 'sistem')
                $router->addRoute('GET', '/v1/users', [UserController::class, 'getData'], ["class" => [$connectionDB, $_SESSION['id']], "method" => []]);
            else
                $router->addRoute('GET', '/v1/users', [UserController::class, 'getDataInvitado'], ["class" => [], "method" => []]);
            $router->addRoute('POST', '/v1/users', [UserController::class, 'register'], ["class" => [$connectionDB], "method" => [$connectionDB, $_INPUT['type'] ?? null, $_INPUT['data'] ?? null]]);

            $router->addRoute('POST', '/v1/products', [ProductController::class, 'purchaseGems'], ["class" => [$connectionDB], "method" => [$_INPUT['LemonSqueezy'] ?? null]]);
            $router->addRoute('GET', '/v1/products', [ProductController::class, 'getAll'], ["class" => [$connectionDB], "method" => ["gems", "CURRENCY"]]);

            if ($uri == "/v1/deckanalyzer" && in_array($_INPUT['type'], ['basic', 'intermediate']))
                $router->addRoute('POST', '/v1/deckanalyzer', [DeckController::class, 'analyze'], ["class" => [$connectionDB], "method" => [json_decode($_INPUT['namesCards'], true), intval($_INPUT['AnaEvo']), $_INPUT['type']]]);

            if ($uri == "/v1/deckbuilder" && $_INPUT['level'] == 'basic')
                $router->addRoute('POST', '/v1/deckbuilder', [DeckController::class, 'create'], ["class" => [$connectionDB], "method" => [$_INPUT]]);

        } else {
            // ... resto de las funciones que no requieren conexión a la base de datos ...
            $router->addRoute('GET', '/v1/cards', [CardController::class, 'showAll'], ["class" => [], "method" => []]);
        }
    }

    if ($TypeAcount == 'google' || $TypeAcount == 'sistem') {
        if (in_array($uri, ['/v1/deckanalyzer', '/v1/users', '/v1/deckbuilder'])) {
            $router->addRoute('PATCH', '/v1/users', [UserController::class, 'update'], ["class" => [$connectionDB], "method" => [$connectionDB, $_INPUT['data'] ?? null]]);

            if ($uri == "/v1/deckbuilder" && ($_INPUT['level'] == 'intermediate' || $_INPUT['level'] == 'advanced'))
                $router->addRoute('POST', '/v1/deckbuilder', [DeckController::class, 'create'], ["class" => [$connectionDB], "method" => [$_INPUT]]);

            if ($uri == "/v1/deckanalyzer" && $_INPUT['type'] == 'advanced')
                $router->addRoute('POST', '/v1/deckanalyzer', [DeckController::class, 'analyze'], ["class" => [$connectionDB], "method" => [json_decode($_INPUT['namesCards'], true), intval($_INPUT['AnaEvo']), $_INPUT['type']]]);
        }
    }

    // Verificar si no se ha ejecutado ninguna acción y el usuario es invitado
/*         if (!$actionExecuted && $TypeAcount == 'invitado') {
            $res['state'] = 'error';
            array_push($res['alerts'], '¡Aún eres invitado!, Regístrate y únete a la fiesta de verdad.');
        } else if (!$actionExecuted) {
            $res['state'] = 'error';
            array_push($res['alerts'], 'No se encontró ninguna acción válida para ejecutar.');
        } else if ($actionExecuted) {
            $res['state'] = 'success';
        } */
} catch (\Exception $e) {
    $router->respond(500, "error", "Error when executing the action: " . $e->getMessage());
} finally {
    // Liberar la conexión solo si se obtuvo
    if ($connectionDB !== null) {
        $DatabasePool->releaseConnection($connectionDB);
    }
}

$router->dispatch($method, $uri);
